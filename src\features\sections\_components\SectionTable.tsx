import * as React from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type SortingState,
} from '@tanstack/react-table'
import {
  MoreHorizontalIcon,
  EditIcon,
  Trash2Icon,
  UsersIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowUpDownIcon,
} from 'lucide-react'
import { useQuery } from 'convex/react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu'
import { api } from 'convex/_generated/api'
import type { Section, SectionId } from '@/lib/types'

interface SectionTableProps {
  gradeLevel: number | null
  searchQuery: string
  onEdit: (sectionId: SectionId) => void
  onDelete: (sectionId: SectionId) => void
}

const columnHelper = createColumnHelper<Section>()

export function SectionTable({ gradeLevel, searchQuery, onEdit, onDelete }: SectionTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])

  const allSections = useQuery(api.sections.getAll)
  const teachers = useQuery(api.teachers.getAll)
  const tracks = useQuery(api.tracks.getAll)
  const strands = useQuery(api.strands.getAll)
  const majors = useQuery(api.majors.getAll)

  const sections = allSections
    ? allSections.filter((section) => {
        const matchesGradeLevel = gradeLevel === null || section.gradeLevel === gradeLevel
        return matchesGradeLevel && section.isActive
      })
    : []

  // Create lookup maps for related data
  const teacherMap = React.useMemo(() => {
    if (!teachers) return new Map()
    return new Map(teachers.map((teacher) => [teacher._id, teacher]))
  }, [teachers])

  const trackMap = React.useMemo(() => {
    if (!tracks) return new Map()
    return new Map(tracks.map((track) => [track._id, track]))
  }, [tracks])

  const strandMap = React.useMemo(() => {
    if (!strands) return new Map()
    return new Map(strands.map((strand) => [strand._id, strand]))
  }, [strands])

  const majorMap = React.useMemo(() => {
    if (!majors) return new Map()
    return new Map(majors.map((major) => [major._id, major]))
  }, [majors])

  const columns = [
    columnHelper.accessor('name', {
      header: 'Section Name',
      cell: (info) => <div className="font-medium">{info.getValue()}</div>,
    }),
    columnHelper.accessor('gradeLevel', {
      header: 'Grade Level',
      cell: (info) => <div className="text-sm">Grade {info.getValue()}</div>,
    }),
    columnHelper.display({
      id: 'adviser',
      header: 'Adviser',
      cell: (info) => {
        const section = info.row.original
        const teacher = teacherMap.get(section.adviserId)

        if (!teachers) {
          return <div className="text-sm text-muted-foreground">Loading...</div>
        }

        if (!teacher) {
          return <div className="text-sm text-muted-foreground">No adviser assigned</div>
        }

        return (
          <div className="text-sm font-medium">
            {teacher.firstName} {teacher.lastName}
          </div>
        )
      },
    }),
    columnHelper.display({
      id: 'track',
      header: 'Track',
      cell: (info) => {
        const section = info.row.original
        const gradeLevel = section.gradeLevel

        if (gradeLevel <= 10) {
          return <span className="text-muted-foreground text-sm">N/A (Junior High)</span>
        }

        if (!tracks) {
          return <div className="text-sm text-muted-foreground">Loading...</div>
        }

        const track = section.trackId ? trackMap.get(section.trackId) : null

        if (!track) {
          return <div className="text-sm text-muted-foreground">No track assigned</div>
        }

        return <div className="text-sm font-medium">{track.name}</div>
      },
    }),
    columnHelper.display({
      id: 'strand',
      header: 'Strand',
      cell: (info) => {
        const section = info.row.original
        const gradeLevel = section.gradeLevel

        if (gradeLevel <= 10) {
          return <span className="text-muted-foreground text-sm">N/A (Junior High)</span>
        }

        if (!strands) {
          return <div className="text-sm text-muted-foreground">Loading...</div>
        }

        const strand = section.strandId ? strandMap.get(section.strandId) : null

        if (!strand) {
          return <div className="text-sm text-muted-foreground">No strand assigned</div>
        }

        return <div className="text-sm font-medium">{strand.name}</div>
      },
    }),
    columnHelper.display({
      id: 'major',
      header: 'Major',
      cell: (info) => {
        const section = info.row.original
        const gradeLevel = section.gradeLevel

        if (gradeLevel <= 10) {
          return <span className="text-muted-foreground text-sm">N/A (Junior High)</span>
        }

        if (!majors) {
          return <div className="text-sm text-muted-foreground">Loading...</div>
        }

        const major = section.majorId ? majorMap.get(section.majorId) : null

        if (!major) {
          return <div className="text-sm text-muted-foreground">No major assigned</div>
        }

        return <div className="text-sm font-medium">{major.name}</div>
      },
    }),
    columnHelper.display({
      id: 'enrollment',
      header: 'Enrollment',
      cell: (info) => {
        const section = info.row.original
        return (
          <div className="text-sm">
            <div className="font-medium">Total: {section.maleCount + section.femaleCount}</div>
            <div className="text-muted-foreground">
              Male: {section.maleCount} | Female: {section.femaleCount}
            </div>
          </div>
        )
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: (info) => {
        const section = info.row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="size-8">
                <MoreHorizontalIcon />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(section._id)}>
                <EditIcon />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(section._id)} variant="destructive">
                <Trash2Icon />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    }),
  ]

  const table = useReactTable({
    data: sections,
    columns,
    state: {
      sorting,
      globalFilter: searchQuery,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: () => {},
    globalFilterFn: 'includesString',
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  if (table.getFilteredRowModel().rows.length === 0) {
    return (
      <div className="p-8 text-center">
        <UsersIcon className="mx-auto text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No sections found</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          No sections match your current filters. Try adjusting your search criteria.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-center">
                    {header.isPlaceholder ? null : (
                      <div
                        className={
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none flex items-center justify-center gap-2'
                            : 'flex justify-center'
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() &&
                          (header.column.getIsSorted() === 'asc' ? (
                            <ArrowUpIcon size={14} />
                          ) : header.column.getIsSorted() === 'desc' ? (
                            <ArrowDownIcon size={14} />
                          ) : (
                            <ArrowUpDownIcon size={14} />
                          ))}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="text-center">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}{' '}
          to{' '}
          {Math.min(
            (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
            table.getFilteredRowModel().rows.length
          )}{' '}
          of {table.getFilteredRowModel().rows.length} results
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(table.getPageCount(), 5) }, (_, i) => {
              const pageIndex =
                table.getPageCount() <= 5
                  ? i
                  : Math.max(
                      0,
                      Math.min(
                        table.getState().pagination.pageIndex - 2 + i,
                        table.getPageCount() - 5 + i
                      )
                    )

              return (
                <Button
                  key={pageIndex}
                  variant={
                    table.getState().pagination.pageIndex === pageIndex ? 'default' : 'outline'
                  }
                  size="sm"
                  onClick={() => table.setPageIndex(pageIndex)}
                  className="size-7 p-0 rounded-md"
                >
                  {pageIndex + 1}
                </Button>
              )
            })}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
